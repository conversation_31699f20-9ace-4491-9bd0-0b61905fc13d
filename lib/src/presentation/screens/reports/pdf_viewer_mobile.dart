import 'package:flutter/material.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'dart:async';
import 'package:flutter_pdfview/flutter_pdfview.dart';

class PDFViewerMobile extends StatelessWidget {
  final String filePath;
  final String fileName;
  final bool enableSwipe;
  final bool swipeHorizontal;
  final bool autoSpacing;
  final bool pageFling;
  final Color backgroundColor;
  final Function(int?)? onRender;
  final Function(dynamic)? onError;
  final Function(int?, dynamic)? onPageError;
  final Function(PDFViewController)? onViewCreated;
  final Function(int?, int?)? onPageChanged;

  const PDFViewerMobile({
    super.key,
    required this.filePath,
    required this.fileName,
    this.enableSwipe = true,
    this.swipeHorizontal = false,
    this.autoSpacing = false,
    this.pageFling = false,
    this.backgroundColor = Colors.white,
    this.onRender,
    this.onError,
    this.onPageError,
    this.onViewCreated,
    this.onPageChanged,
  });

  @override
  Widget build(BuildContext context) {
    return PDFViewerScreen(
      filePath: filePath,
      fileName: fileName,
      enableSwipe: enableSwipe,
      swipeHorizontal: swipeHorizontal,
      autoSpacing: autoSpacing,
      pageFling: pageFling,
      backgroundColor: backgroundColor,
      onRender: onRender,
      onError: onError,
      onPageError: onPageError,
      onViewCreated: onViewCreated,
      onPageChanged: onPageChanged,
    );
  }
}

// Embedded PDF Viewer for Mobile (without Scaffold)
class EmbeddedPDFViewerMobile extends StatefulWidget {
  final String filePath;
  final String fileName;
  final bool enableSwipe;
  final bool swipeHorizontal;
  final bool autoSpacing;
  final bool pageFling;
  final Color backgroundColor;
  final Function(int?)? onRender;
  final Function(dynamic)? onError;
  final Function(int?, dynamic)? onPageError;
  final Function(PDFViewController)? onViewCreated;
  final Function(int?, int?)? onPageChanged;
  final bool showPageIndicator;
  final bool showZoomControls;

  const EmbeddedPDFViewerMobile({
    super.key,
    required this.filePath,
    required this.fileName,
    this.enableSwipe = true,
    this.swipeHorizontal = false,
    this.autoSpacing = false,
    this.pageFling = false,
    this.backgroundColor = Colors.white,
    this.onRender,
    this.onError,
    this.onPageError,
    this.onViewCreated,
    this.onPageChanged,
    this.showPageIndicator = true,
    this.showZoomControls = true,
  });

  @override
  State<EmbeddedPDFViewerMobile> createState() => _EmbeddedPDFViewerMobileState();
}

class _EmbeddedPDFViewerMobileState extends State<EmbeddedPDFViewerMobile> {
  final Completer<PDFViewController> _controller = Completer<PDFViewController>();
  int? pages = 0;
  int currentPage = 1;
  bool isReady = false;
  String errorMessage = '';
  double _zoomLevel = 1.0;

  void _zoomIn() {
    setState(() {
      _zoomLevel = (_zoomLevel * 1.2).clamp(0.5, 3.0);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = (_zoomLevel / 1.2).clamp(0.5, 3.0);
    });
  }

  void _resetZoom() {
    setState(() {
      _zoomLevel = 1.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            if (widget.filePath.isNotEmpty)
              PDFView(
                filePath: widget.filePath,
                enableSwipe: widget.enableSwipe,
                swipeHorizontal: widget.swipeHorizontal,
                autoSpacing: widget.autoSpacing,
                pageFling: widget.pageFling,
                backgroundColor: widget.backgroundColor,
                onRender: (pagesCount) {
                  setState(() {
                    pages = pagesCount;
                    isReady = true;
                  });
                  if (widget.onRender != null) {
                    widget.onRender!(pagesCount);
                  }
                },
                onError: (error) {
                  setState(() {
                    errorMessage = error.toString();
                  });
                  debugPrint('Embedded PDF Error: ${error.toString()}');
                  if (widget.onError != null) {
                    widget.onError!(error);
                  }
                },
                onPageError: (page, error) {
                  debugPrint('Embedded PDF Page Error - Page $page: ${error.toString()}');
                  if (widget.onPageError != null) {
                    widget.onPageError!(page, error);
                  }
                },
                onPageChanged: (int? page, int? total) {
                  setState(() {
                    currentPage = (page ?? 0) + 1;
                  });
                  if (widget.onPageChanged != null) {
                    widget.onPageChanged!(page, total);
                  }
                },
                onViewCreated: (PDFViewController pdfViewController) {
                  _controller.complete(pdfViewController);
                  if (widget.onViewCreated != null) {
                    widget.onViewCreated!(pdfViewController);
                  }
                },
              )
            else
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No PDF file path provided',
                      style: AppFonts.regularTextStyle(16, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            if (!isReady && widget.filePath.isNotEmpty)
              Container(
                color: Colors.white.withValues(alpha: 0.8),
                child: const Center(child: CircularProgressIndicator()),
              ),
            if (errorMessage.isNotEmpty)
              Container(
                color: Colors.white,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading PDF',
                        style: AppFonts.semiBoldTextStyle(18, color: AppTheme.primaryTextColor),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            // Page indicator overlay
            if (widget.showPageIndicator && isReady && pages! > 1)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$currentPage / $pages',
                    style: AppFonts.mediumTextStyle(12, color: Colors.white),
                  ),
                ),
              ),
            // Zoom Controls
            if (widget.showZoomControls && isReady)
              Positioned(
                top: 16,
                left: 16,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.zoom_in, size: 20),
                        onPressed: _zoomIn,
                        tooltip: 'Zoom In',
                        constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          '${(_zoomLevel * 100).toInt()}%',
                          style: AppFonts.regularTextStyle(10, color: Colors.grey[700]),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.zoom_out, size: 20),
                        onPressed: _zoomOut,
                        tooltip: 'Zoom Out',
                        constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
                      ),
                      IconButton(
                        icon: const Icon(Icons.fit_screen, size: 20),
                        onPressed: _resetZoom,
                        tooltip: 'Fit to Screen',
                        constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class PDFViewerScreen extends StatefulWidget {
  final String filePath;
  final String fileName;
  final bool enableSwipe;
  final bool swipeHorizontal;
  final bool autoSpacing;
  final bool pageFling;
  final Color backgroundColor;
  final Function(int?)? onRender;
  final Function(dynamic)? onError;
  final Function(int?, dynamic)? onPageError;
  final Function(PDFViewController)? onViewCreated;
  final Function(int?, int?)? onPageChanged;

  const PDFViewerScreen({
    super.key,
    required this.filePath,
    required this.fileName,
    this.enableSwipe = true,
    this.swipeHorizontal = false,
    this.autoSpacing = false,
    this.pageFling = false,
    this.backgroundColor = Colors.white,
    this.onRender,
    this.onError,
    this.onPageError,
    this.onViewCreated,
    this.onPageChanged,
  });

  @override
  State<PDFViewerScreen> createState() => _PDFViewerScreenState();
}

class _PDFViewerScreenState extends State<PDFViewerScreen> {
  final Completer<PDFViewController> _controller = Completer<PDFViewController>();
  int? pages = 0;
  int currentPage = 1;
  bool isReady = false;
  String errorMessage = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      appBar: AppBar(
        title: Text(
          widget.fileName,
          style: AppFonts.semiBoldTextStyle(18, color: AppTheme.primaryTextColor),
        ),
        backgroundColor: Colors.white,
        foregroundColor: AppTheme.primaryTextColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          if (isReady)
            Container(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$currentPage / ${pages ?? 0}',
                style: AppFonts.mediumTextStyle(14, color: AppTheme.primaryColor),
              ),
            ),
        ],
      ),
      body: Container(
        margin: const EdgeInsets.all(defaultPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              if (widget.filePath.isNotEmpty)
                PDFView(
                  filePath: widget.filePath,
                  enableSwipe: widget.enableSwipe,
                  swipeHorizontal: widget.swipeHorizontal,
                  autoSpacing: widget.autoSpacing,
                  pageFling: widget.pageFling,
                  backgroundColor: widget.backgroundColor,
                  onRender: (pagesCount) {
                    setState(() {
                      pages = pagesCount;
                      isReady = true;
                    });
                    if (widget.onRender != null) {
                      widget.onRender!(pagesCount);
                    }
                  },
                  onError: (error) {
                    setState(() {
                      errorMessage = error.toString();
                    });
                    debugPrint('PDF Error: ${error.toString()}');
                    if (widget.onError != null) {
                      widget.onError!(error);
                    }
                  },
                  onPageError: (page, error) {
                    debugPrint('PDF Page Error - Page $page: ${error.toString()}');
                    if (widget.onPageError != null) {
                      widget.onPageError!(page, error);
                    }
                  },
                  onViewCreated: (PDFViewController pdfViewController) {
                    _controller.complete(pdfViewController);
                    if (widget.onViewCreated != null) {
                      widget.onViewCreated!(pdfViewController);
                    }
                  },
                  onPageChanged: (int? page, int? total) {
                    setState(() {
                      currentPage = (page ?? 0) + 1;
                    });
                    debugPrint('PDF Page changed: $page/$total');
                    if (widget.onPageChanged != null) {
                      widget.onPageChanged!(page, total);
                    }
                  },
                )
              else
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.picture_as_pdf,
                        size: 64,
                        color: Colors.grey[400],
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No PDF file path provided',
                        style: AppFonts.regularTextStyle(16, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              if (!isReady && widget.filePath.isNotEmpty)
                Container(
                  color: Colors.white.withValues(alpha: 0.8),
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
              if (errorMessage.isNotEmpty)
                Container(
                  color: Colors.white,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Error loading PDF',
                          style: AppFonts.semiBoldTextStyle(18, color: AppTheme.primaryTextColor),
                        ),
                        const SizedBox(height: 8),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32),
                          child: Text(
                            errorMessage,
                            textAlign: TextAlign.center,
                            style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
                          ),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              errorMessage = '';
                              isReady = false;
                            });
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

