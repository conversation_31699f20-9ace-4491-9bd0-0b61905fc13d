import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:neorevv/src/core/config/app_strings.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';
import 'package:dio/dio.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:async';

// Web-specific imports (conditional)
// ignore: avoid_web_libraries_in_flutter, deprecated_member_use
import 'dart:html' as html;
// ignore: avoid_web_libraries_in_flutter
import 'dart:ui_web' as ui_web;

import 'package:neorevv/src/presentation/shared/components/elevated_button.dart';

class PdfPreviewWidget extends StatefulWidget {
  final ReportModel report;

  const PdfPreviewWidget({super.key, required this.report});

  @override
  State<PdfPreviewWidget> createState() => _PdfPreviewWidgetState();
}

class _PdfPreviewWidgetState extends State<PdfPreviewWidget> {
  bool isPdfLoaded = false;

  // PDF loading state
  bool isLoadingPdf = false;
  bool hasError = false;
  String errorMessage = '';
  Uint8List? pdfBytes;
  String? pdfFilePath;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    setState(() {
      isLoadingPdf = true;
      hasError = false;
      errorMessage = '';
      isPdfLoaded = false;
    });

    try {
      if (widget.report.url.isNotEmpty) {
        await _loadRemotePdf();
        setState(() {
          isPdfLoaded = true;
        });
      } else {
        setState(() {
          isLoadingPdf = false;
          hasError = true;
          errorMessage = 'No PDF URL provided in the report data';
          isPdfLoaded = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading PDF: $e');
      setState(() {
        isLoadingPdf = false;
        hasError = true;
        errorMessage = 'Failed to load PDF from URL: $e';
        isPdfLoaded = false;
      });
    }
  }

  Future<void> _loadRemotePdf() async {
    debugPrint('Loading PDF from URL: ${widget.report.url}');
    final dio = Dio();
    final response = await dio.get(
      widget.report.url,
      options: Options(responseType: ResponseType.bytes),
    );

    if (response.statusCode == 200 && response.data != null) {
      final bytes = Uint8List.fromList(response.data);

      // For mobile, save to temporary file
      if (!kIsWeb) {
        await _savePdfToTempFile(bytes);
      }

      setState(() {
        pdfBytes = bytes;
        isLoadingPdf = false;
      });
      debugPrint('PDF loaded successfully: ${pdfBytes!.length} bytes');
    } else {
      throw Exception('Failed to load PDF: HTTP ${response.statusCode}');
    }
  }

  Future<void> _savePdfToTempFile(Uint8List bytes) async {
    try {
      // For mobile platforms, we need to save the PDF to a temporary file
      // This is a simplified approach - in production you'd use path_provider
      final tempDir =
          '/tmp'; // This would be replaced with proper temp directory
      final fileName =
          'temp_${widget.report.id}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      pdfFilePath = '$tempDir/$fileName';

      debugPrint('PDF would be saved to: $pdfFilePath');
    } catch (e) {
      debugPrint('Error saving PDF to temp file: $e');
    }
  }

  Future<void> _handleDownload() async {
    if (!isPdfLoaded || widget.report.url.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              const Text(pdfNotAvailableForDownload),
            ],
          ),
          backgroundColor: AppTheme.textFieldMandatoryColor,
          duration: const Duration(seconds: 2),
        ),
      );
      return;
    }

    try {
      // Show downloading snackbar
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  '$downloading ${widget.report.title}$pdf',
                  style: AppFonts.regularTextStyle(14, color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: AppTheme.selectedComboBoxBorder,
          duration: const Duration(seconds: 2),
        ),
      );

      if (kIsWeb) {
        // Web download implementation
        await _downloadForWeb();
      } else {
        // Mobile download implementation
        await _downloadForMobile();
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${widget.report.title}$pdfDownloadedSuccessfully',
                    style: AppFonts.regularTextStyle(14, color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '$failedToDownloadPDF ${e.toString()}',
                    style: AppFonts.regularTextStyle(14, color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: AppTheme.textFieldMandatoryColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _downloadForWeb() async {
    try {
      // First, fetch the PDF data to ensure we can download it properly
      final dio = Dio();
      final response = await dio.get(
        widget.report.url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final bytes = response.data as List<int>;

        // Create blob from the PDF bytes
        final blob = html.Blob([bytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);

        // Create download link without target="_blank" to force download
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', '${widget.report.title}.pdf')
          ..style.display = 'none'; // Hide the anchor element

        // Add to DOM, click, and remove
        html.document.body?.children.add(anchor);
        anchor.click();
        html.document.body?.children.remove(anchor);

        // Clean up the blob URL
        html.Url.revokeObjectUrl(url);
      } else {
        throw Exception('Failed to fetch PDF: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Web download error: $e');
      rethrow;
    }
  }

  Future<void> _downloadForMobile() async {
    try {
      // Handle remote URLs
      final dio = Dio();
      final response = await dio.get(
        widget.report.url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        final bytes = response.data as List<int>;

        // Create blob and download link
        final blob = html.Blob([bytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);

        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', '${widget.report.title}.pdf')
          ..style.display = 'none'; // Hide the anchor element

        html.document.body?.children.add(anchor);
        anchor.click();
        html.document.body?.children.remove(anchor);

        // Clean up the blob URL
        html.Url.revokeObjectUrl(url);
      } else {
        throw Exception('Failed to download PDF: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Mobile download error: $e');
      rethrow;
    }
  }

  Widget _buildWebPdfViewer() {
    if (pdfBytes == null) return const SizedBox();

    return _EmbeddedPDFViewerWeb(
      fileBytes: pdfBytes!,
      fileName: widget.report.title,
    );
  }

  Widget _buildMobilePdfViewer() {
    if (pdfBytes == null || pdfFilePath == null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[100],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
              '$iconAssetpath/files_icons.png',
              width: 16,
              height: 16,
              // fit: BoxFit.contain,
            ),
              // Icon(Icons.picture_as_pdf, size: 64, color: Colors.red[400]),
              const SizedBox(height: 16),
              Text(
                pdfViewer,
                style: AppFonts.semiBoldTextStyle(18, color: Colors.grey[700]),
              ),
              const SizedBox(height: 8),
              Text(
                preparingPDFForMobileViewing,
                style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return _EmbeddedPDFViewerMobile(
      filePath: pdfFilePath!,
      fileName: widget.report.title,
    );
  }

  Widget _buildPdfInfoPanelContent() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.reportBg,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // PDF Icon and Title Section
          Container(
            padding: const EdgeInsets.only(
              top: defaultPadding * 2,
              left: defaultPadding,
              right: defaultPadding,
              bottom: 0,
            ),
            // padding: const EdgeInsets.all(defaultPadding),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // PDF Icon
                SizedBox(
                  width: 16 * 2 * 1.4,
                  height: 16 * 2 * 1.4,
                  child: Image.asset('$iconAssetpath/files_icons.png'),
                ),
                const SizedBox(width: 12),

                // PDF Title
                Expanded(
                  child: Text(
                    '${widget.report.title}.pdf',
                    style:
                        AppFonts.semiBoldTextStyle(
                          16,
                          color: AppTheme.primaryTextColor,
                        ).copyWith(
                          height: 1.5, // Line height multiplier
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // Action Buttons Section
          Container(
            padding: const EdgeInsets.all(defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(width: 12),
                // Download Button
                SizedBox(
                  width: ResponsiveSizes.applyButtonWidth(context),
                  height: 45,
                  child: AppButton(
                    label: download,
                    onPressed: isPdfLoaded ? _handleDownload : null,
                    backgroundColor: AppTheme.downloadBtn,
                    foregroundColor: Colors.black,
                    borderRadius: 30,
                    elevation: 0,
                    textStyle: AppFonts.mediumTextStyle(
                      14,
                      color: Colors.black,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPdfViewerContent() {
    if (isLoadingPdf) {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(loadingPDF),
            ],
          ),
        ),
      );
    }

    if (hasError) {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
              const SizedBox(height: 16),
              Text(
                errorLoadingPDF,
                style: AppFonts.semiBoldTextStyle(
                  18,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
                ),
              ),
              const SizedBox(height: 16),
              AppButton(
                label: retry,
                onPressed: _loadPdf,
                backgroundColor: AppTheme.primaryColor,
                textStyle: AppFonts.mediumTextStyle(14),
              ),
            ],
          ),
        ),
      );
    }

    if (pdfBytes != null) {
      return Padding(
        padding: const EdgeInsets.only(
          left: defaultPadding * 3,
          right: defaultPadding * 3,
          bottom: defaultPadding * 2,
        ),
        child: kIsWeb ? _buildWebPdfViewer() : _buildMobilePdfViewer(),
      );
    }

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              '$iconAssetpath/files_icons.png',
              width: 16,
              height: 16,
              // fit: BoxFit.contain,
            ),
            const SizedBox(height: 16),
            Text(
              noPDFAvailable,
              style: AppFonts.semiBoldTextStyle(18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              selectReportToView,
              style: AppFonts.regularTextStyle(14, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final showInfoPanel = screenWidth > 1200;
    final isMobileLayout = screenWidth <= 900;
    final isVerySmallScreen = screenWidth <= 500;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: showInfoPanel
          ? _buildWithInfoPanel()
          : _buildWithoutInfoPanel(
              isMobileLayout: isMobileLayout,
              isVerySmallScreen: isVerySmallScreen,
            ),
    );
  }

  Widget _buildWithInfoPanel() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // PDF Viewer Panel (Left side - takes most space)
        Expanded(
          flex: 3,
          child: Container(
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
            ),
            child: _buildPdfViewerColumn(
              showDownloadIcon: false,
              isMobileLayout: false,
              isVerySmallScreen: false,
            ),
          ),
        ),

        // Vertical divider
        Container(width: 1, color: Colors.grey[200]),

        // PDF Info Panel (Right side - fixed width)
        Container(
          width: ResponsiveSizes.reportSidebarWidth(context),
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
          ),
          child: _buildPdfInfoPanelContent(),
        ),
      ],
    );
  }

  Widget _buildWithoutInfoPanel({
    bool isMobileLayout = false,
    bool isVerySmallScreen = false,
  }) {
    return Container(
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
      child: _buildPdfViewerColumn(
        showDownloadIcon: true,
        isMobileLayout: isMobileLayout,
        isVerySmallScreen: isVerySmallScreen,
      ),
    );
  }

  Widget _buildPdfViewerColumn({
    required bool showDownloadIcon,
    bool isMobileLayout = false,
    bool isVerySmallScreen = false,
  }) {
    // Debug information
    debugPrint('PDF Viewer Column - showDownloadIcon: $showDownloadIcon, isMobileLayout: $isMobileLayout, isVerySmallScreen: $isVerySmallScreen');

    // Responsive font size based on layout
    final titleFontSize = isVerySmallScreen
        ? 18.0
        : isMobileLayout
        ? 20.0
        : 25.0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // PDF Title Header
        Container(
          width: double.infinity,
          padding: EdgeInsets.only(
            top: isVerySmallScreen
                ? defaultPadding * 0.8
                : isMobileLayout
                ? defaultPadding
                : defaultPadding * 2,
            left: isVerySmallScreen
                ? defaultPadding * 0.8
                : isMobileLayout
                ? defaultPadding
                : defaultPadding * 2.3,
            right: showDownloadIcon
                ? (isVerySmallScreen
                    ? defaultPadding * 2.5  // Increased for very small screens
                    : isMobileLayout
                    ? defaultPadding * 2.5  // Increased for mobile
                    : defaultPadding * 2.5)  // Increased for desktop with download icon
                : (isVerySmallScreen
                    ? defaultPadding * 0.8
                    : isMobileLayout
                    ? defaultPadding
                    : defaultPadding * 2.3),
            bottom: isVerySmallScreen ? defaultPadding * 0.8 : defaultPadding,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(12),
              topRight: showDownloadIcon
                  ? const Radius.circular(12)
                  : Radius.zero,
            ),
          ),
          child: Row(
            children: [
              if (!isMobileLayout) const SizedBox(width: 12),

              // Title
              Expanded(
                child: Text(
                  widget.report.title,
                  style: AppFonts.semiBoldTextStyle(
                    titleFontSize,
                    color: AppTheme.primaryTextColor,
                  ),
                  maxLines: showDownloadIcon
                      ? 2
                      : 1, // 2 lines when download icon is shown
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              // Download Icon (only when info panel is hidden)
              if (showDownloadIcon) ...[
                const SizedBox(width: 12),
                IconButton(
                  onPressed: isPdfLoaded ? _handleDownload : null,
                  icon: Image.asset(
                    '$iconAssetpath/fi-rs-download.png',
                    height: isVerySmallScreen
                        ? 18
                        : isMobileLayout
                        ? 20
                        : 24,
                    width: isVerySmallScreen
                        ? 18
                        : isMobileLayout
                        ? 20
                        : 24,
                    color: isPdfLoaded ? AppTheme.downloadIcon : Colors.grey,
                  ),
                  tooltip: downloadPDF,
                  style: IconButton.styleFrom(
                    // backgroundColor: isPdfLoaded
                    //     ? AppTheme.downloadBtn.withValues(alpha: 0.1)
                    //     : Colors.grey.withValues(alpha: 0.1),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding: EdgeInsets.all(
                      isVerySmallScreen
                          ? 6
                          : isMobileLayout
                          ? 8
                          : 12,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),

        // PDF Viewer Content
        Expanded(
          child: ClipRRect(
            borderRadius: BorderRadius.only(
              bottomLeft: const Radius.circular(12),
              bottomRight: showDownloadIcon
                  ? const Radius.circular(12)
                  : Radius.zero,
            ),
            child: _buildPdfViewerContent(),
          ),
        ),
      ],
    );
  }
}

// Embedded PDF Viewer for Web
class _EmbeddedPDFViewerWeb extends StatefulWidget {
  final Uint8List fileBytes;
  final String fileName;

  const _EmbeddedPDFViewerWeb({
    required this.fileBytes,
    required this.fileName,
  });

  @override
  State<_EmbeddedPDFViewerWeb> createState() => _EmbeddedPDFViewerWebState();
}

class _EmbeddedPDFViewerWebState extends State<_EmbeddedPDFViewerWeb> {
  double _zoomLevel = 1.0;
  final String _viewerId =
      'embedded-pdf-viewer-${DateTime.now().millisecondsSinceEpoch}';
  late String _url;

  @override
  void initState() {
    super.initState();
    final blob = html.Blob([widget.fileBytes], 'application/pdf');
    _url = html.Url.createObjectUrlFromBlob(blob);
    _registerViewFactory();
  }

  @override
  void dispose() {
    html.Url.revokeObjectUrl(_url);
    super.dispose();
  }

  void _registerViewFactory() {
    ui_web.platformViewRegistry.registerViewFactory(_viewerId, (int viewId) {
      // Create a container div with white background
      final container = html.DivElement()
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.backgroundColor = '#ffffff'
        ..style.overflow = 'hidden'
        ..style.position = 'relative'
        ..style.display = 'flex'
        ..style.justifyContent = 'center'
        ..style.alignItems = 'center';

      // Add custom CSS to ensure white background and remove black borders
      final style = html.StyleElement()
        ..text = '''
          iframe {
            background-color: #ffffff !important;
            border: none !important;
          }
          body {
            background-color: #ffffff !important;
            margin: 0 !important;
            padding: 0 !important;
          }
        ''';
      html.document.head?.append(style);

      final iframe = html.IFrameElement()
        ..src =
            '$_url#toolbar=1&navpanes=0&scrollbar=1&view=FitH&zoom=${(_zoomLevel * 100).toInt()}'
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.backgroundColor = '#ffffff'
        ..style.border = 'none'
        ..style.display = 'block'
        ..setAttribute('allowfullscreen', 'true');

      container.append(iframe);
      return container;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          // PDF Viewer
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              color: Colors.white,
              child: HtmlElementView(viewType: _viewerId),
            ),
          ),
        ],
      ),
    );
  }
}

// Embedded PDF Viewer for Mobile
class _EmbeddedPDFViewerMobile extends StatefulWidget {
  final String filePath;
  final String fileName;

  const _EmbeddedPDFViewerMobile({
    required this.filePath,
    required this.fileName,
  });

  @override
  State<_EmbeddedPDFViewerMobile> createState() =>
      _EmbeddedPDFViewerMobileState();
}

class _EmbeddedPDFViewerMobileState extends State<_EmbeddedPDFViewerMobile> {
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();
  int? pages = 0;
  int currentPage = 1;
  bool isReady = false;
  String errorMessage = '';

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            if (widget.filePath.isNotEmpty)
              PDFView(
                filePath: widget.filePath,
                enableSwipe: true,
                swipeHorizontal: false,
                autoSpacing: false,
                pageFling: false,
                backgroundColor: Colors.white,
                onRender: (pagesCount) {
                  setState(() {
                    pages = pagesCount;
                    isReady = true;
                  });
                },
                onError: (error) {
                  setState(() {
                    errorMessage = error.toString();
                  });
                  debugPrint('Embedded PDF Error: ${error.toString()}');
                },
                onPageError: (page, error) {
                  debugPrint(
                    'Embedded PDF Page Error - Page $page: ${error.toString()}',
                  );
                },
                onPageChanged: (int? page, int? total) {
                  setState(() {
                    currentPage = (page ?? 0) + 1;
                  });
                },
                onViewCreated: (PDFViewController pdfViewController) {
                  _controller.complete(pdfViewController);
                },
              )
            else
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      '$iconAssetpath/files_icons.png',
                      width: 16,
                      height: 16,
                      // fit: BoxFit.contain,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      noPdfFilePathProvided,
                      style: AppFonts.regularTextStyle(
                        16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            if (!isReady && widget.filePath.isNotEmpty)
              Container(
                color: Colors.white.withValues(alpha: 0.8),
                child: const Center(child: CircularProgressIndicator()),
              ),
            if (errorMessage.isNotEmpty)
              Container(
                color: Colors.white,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: AppTheme.textFieldMandatoryColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        errorLoadingPDF,
                        style: AppFonts.semiBoldTextStyle(
                          18,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            // Page indicator overlay
            if (isReady && pages! > 1)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$currentPage / $pages',
                    style: AppFonts.mediumTextStyle(12, color: Colors.white),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
