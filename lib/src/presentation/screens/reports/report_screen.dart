import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';
import 'components/report_sidebar.dart';
import 'components/pdf_preview_widget.dart';

class ReportScreen extends HookWidget {
  const ReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final selectedReport = useState<ReportModel?>(null);
    final reports = useState<List<ReportModel>>(reportsListData);

    // Set initial selected report
    useEffect(() {
      if (reports.value.isNotEmpty) {
        try {
          selectedReport.value = reports.value.firstWhere((r) => r.isSelected);
        } catch (e) {
          // If no report is selected, select the first one
          selectedReport.value = reports.value.first;
        }
      } else {
        selectedReport.value = null;
      }
      return null;
    }, []);

    void onReportSelected(ReportModel report) {
      // Update selection state
      final updatedReports = reports.value.map((r) {
        return r.copyWith(isSelected: r.id == report.id);
      }).toList();

      reports.value = updatedReports;
      selectedReport.value = report;
    }

    Widget buildNoReportsAvailable() {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.folder_open_outlined,
                size: 80,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 24),
              Text(
                'No Reports Available',
                style: AppFonts.semiBoldTextStyle(
                  24,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'There are currently no reports to display.\nPlease check back later or contact your administrator.',
                textAlign: TextAlign.center,
                style: AppFonts.regularTextStyle(
                  16,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: () {
                  // You can add refresh functionality here
                  // For now, it just shows a message
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Refresh functionality can be implemented here'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    double getResponsiveSidebarWidth(double screenWidth) {
      if (screenWidth > 1200) {
        // Width > 1200: Responsive sidebar width
        return screenWidth > 1400 ? 280.0 : 260.0;
      } else {
        // Width <= 1200: Constant sidebar width
        return 300.0;
      }
    }

    Widget buildDropdownLayout(
      List<ReportModel> reports,
      ReportModel? selectedReport,
      Function(ReportModel) onReportSelected,
      double pdfPanelHeight,
    ) {

      return Column(
        children: [
          // Dropdown for reports (width <= 500)
          Container(
            width: double.infinity,
            height: 50, // Reduced height
            margin: const EdgeInsets.only(bottom: defaultPadding),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: DropdownButton<String>(
              value: selectedReport?.id, // Use ID instead of object
              hint: Text(
                'Select Report',
                style: AppFonts.regularTextStyle(14, color: Colors.grey[600]!),
              ),
              isExpanded: true,
              underline: const SizedBox(), // Remove default underline
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: Colors.grey[600],
              ),
              items: reports.map((report) {
                return DropdownMenuItem<String>(
                  value: report.id, // Use unique ID
                  child: Row(
                    children: [
                      Image.asset(
                        '$iconAssetpath/fi-rs-copy-alt.png',
                        width: 16,
                        height: 16,
                        color: AppTheme.black,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          report.title,
                          style: AppFonts.regularTextStyle(14, color: AppTheme.black),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (String? reportId) {
                if (reportId != null) {
                  final report = reports.firstWhere((r) => r.id == reportId);
                  onReportSelected(report);
                }
              },
            ),
          ),

          // PDF viewer below dropdown
          Expanded(
            child: selectedReport != null
                ? PdfPreviewWidget(
                    key: ValueKey(selectedReport.id),
                    report: selectedReport,
                  )
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Center(
                      child: Text('Select a report to preview'),
                    ),
                  ),
          ),
        ],
      );
    }

    Widget buildMobileLayout(
      List<ReportModel> reports,
      ReportModel? selectedReport,
      Function(ReportModel) onReportSelected,
      double sidebarHeight,
      double pdfPanelHeight,
    ) {
      return Column(
        children: [
          // Sidebar at top for mobile (width 500-900)
          Container(
            width: double.infinity,
            height: 200, // Fixed height for mobile sidebar
            padding: const EdgeInsets.all(defaultPadding),
            margin: const EdgeInsets.only(bottom: defaultPadding),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
            ),
            child: ReportSidebar(
              reports: reports,
              onReportSelected: onReportSelected,
            ),
          ),

          // PDF viewer below sidebar
          Expanded(
            child: selectedReport != null
                ? PdfPreviewWidget(
                    key: ValueKey(selectedReport.id),
                    report: selectedReport,
                  )
                : Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withValues(alpha: 0.1),
                          spreadRadius: 1,
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Center(
                      child: Text('Select a report to preview'),
                    ),
                  ),
          ),
        ],
      );
    }

    Widget buildDesktopLayout(
      List<ReportModel> reports,
      ReportModel? selectedReport,
      Function(ReportModel) onReportSelected,
      double sidebarWidth,
      double sidebarHeight,
      double pdfPanelHeight,
    ) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left Panel - Report Sidebar
          Container(
            width: sidebarWidth,
            height: sidebarHeight,
            padding: const EdgeInsets.all(defaultPadding),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.white,
            ),
            child: ReportSidebar(
              reports: reports,
              onReportSelected: onReportSelected,
            ),
          ),

          const SizedBox(width: defaultPadding),

          // Right Panel - PDF Preview
          Expanded(
            child: SizedBox(
              height: pdfPanelHeight,
              child: selectedReport != null
                  ? PdfPreviewWidget(
                      key: ValueKey(selectedReport.id),
                      report: selectedReport,
                    )
                  : Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            spreadRadius: 1,
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Center(
                        child: Text('Select a report to preview'),
                      ),
                    ),
            ),
          ),
        ],
      );
    }

    // Responsive sidebar width and layout
    final screenWidth = MediaQuery.of(context).size.width;
    final sidebarWidth = getResponsiveSidebarWidth(screenWidth);
    final sidebarHeight = ResponsiveSizes.reportSidebarHeight(context);
    final isMobileLayout = screenWidth <= 900;
    final isDropdownLayout = screenWidth <= 500;
    final pdfPanelHeight = ResponsiveSizes.pdfPreviewPanelHeight(context);


    return Builder(
      builder: (context) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
        // Reports Heading
        Container(
          padding: const EdgeInsets.only(bottom: defaultPadding),
          child: Row(
            children: [
              Image.asset(
                '$iconAssetpath/fi-rr-document.png',
                width: 20,
                height: 20,
                color: AppTheme.black,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  'Reports',
                  style: AppFonts.semiBoldTextStyle(
                    22,
                    color: AppTheme.black,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Main Content - Responsive Layout
        Flexible(
          fit: FlexFit.loose,
          child: reports.value.isEmpty
              ? buildNoReportsAvailable()
              : isDropdownLayout
                  ? buildDropdownLayout(
                      reports.value,
                      selectedReport.value,
                      onReportSelected,
                      pdfPanelHeight,
                    )
                  : isMobileLayout
                      ? buildMobileLayout(
                          reports.value,
                          selectedReport.value,
                          onReportSelected,
                          sidebarHeight,
                          pdfPanelHeight,
                        )
                      : buildDesktopLayout(
                          reports.value,
                          selectedReport.value,
                          onReportSelected,
                          sidebarWidth,
                          sidebarHeight,
                          pdfPanelHeight,
                        ),
        ),
      ],
    );
      },
    );
  }
}
