import 'package:flutter/material.dart';

const fontFamily = 'Poppins';

// Padding
const primaryLayoutPadding = 22.0;
const defaultPadding = 16.0;

//margin
const webLayoutmargin =
    40.0; // wholecontent will be dispalyed within this boundary
const defaultMargin = 16.0;

// Screen Sizes
const smallMobileBreakpoint = 500;
const mobileBreakpoint = 800;
const tabletBreakpoint = 1200;
const desktopBreakpoint = 1400;

const sideDrawerBreakpoint = 1000;
const commissionCardBreakPoint = 1100;

// Assets
const imageAssetpath = 'assets/images';
const iconAssetpath = 'assets/icons';
const launcherAssetpath = 'assets/launcher';

// Dashboard Tab
const int mainTabsCount = 6;
const int registerBrokerTabIndex = 6;
const int agentNetworkTabIndex = 7;
const Duration animationDuration = Duration(milliseconds: 450);
const Duration staggerDelay = Duration(milliseconds: 80);
const double mobileHorizontalPadding = 12.0;
const double slideOffset = 30.0;
const double maxBlurRadius = 1.5;
const double scaleStart = 0.988;

// Responsive Helper Functions
// Agent Table
class ResponsiveSizes {
  // Icon sizes
  static double iconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return MediaQuery.of(context).size.width * 0.06; // ~45 for mobile
    } else if (screenWidth < tabletBreakpoint) {
      return MediaQuery.of(context).size.width * 0.045; // ~55 for tablet
    } else {
      return MediaQuery.of(context).size.width * 0.04; // ~55 for desktop
    }
  }

  // Button heights
  static double buttonHeight(BuildContext context) {
    return MediaQuery.of(context).size.height * 0.06; // ~50
  }

  // Font sizes
  static double largeFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return MediaQuery.of(context).size.width * 0.035; // ~25 for mobile
    } else {
      return MediaQuery.of(context).size.width * 0.025; // ~35 for desktop
    }
  }

  // Container widths
  static double filterButtonWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.22; // ~110 for 500px width, ~88 for 400px
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.15; // ~107 for mobile
    } else {
      return screenWidth * 0.05; // ~107 for tablet/desktop
    }
  }

  static double searchFieldWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.55; // ~275 for 500px width, ~220 for 400px
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.4; // ~285 for mobile
    } else {
      return screenWidth * 0.15; // ~223 for tablet/desktop
    }
  }

  static double dropdownWidth(BuildContext context) {
    return MediaQuery.of(context).size.width * 0.16; // ~240
  }

  static double statusChipWidth(BuildContext context) {
    return MediaQuery.of(context).size.width * 0.065; // ~96
  }

  static double comboBoxWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.8; // Full width for small mobile
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.4; // 40% width for mobile
    } else if (screenWidth < tabletBreakpoint) {
      return screenWidth * 0.2; // 20% width for tablet
    } else {
      return 240.0; // Fixed width for desktop
    }
  }

  // For apply button in filter
  static double applyButtonWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 500) {
      // Small mobile
      return screenWidth * 0.8; // Full width for small mobile
    } else if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.3; // 30% width for mobile
    } else if (screenWidth < tabletBreakpoint) {
      return screenWidth * 0.15; // 15% width for tablet
    } else {
      return 136.0; // Same as combo boxes for desktop
    }
  }

  static double reportSidebarWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return screenWidth * 0.95; // 95% width for mobile
    } else if (screenWidth < tabletBreakpoint) {
      return screenWidth * 0.6; // 60% width for tablet
    } else {
      return 320.0; // Fixed width for desktop
    }
  }

  // For report sidebar height
  static double reportSidebarHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < mobileBreakpoint) {
      return screenHeight * 0.8; // 80% height for mobile
    } else if (screenHeight < 800) {
      return screenHeight * 0.75; // 75% height for smaller screens
    } else {
      return 725.0; // Fixed height for desktop
    }
  }

  // For report item dimensions
  static double reportItemHeight(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) {
      return 45.0; // Compact height for small screens
    } else {
      return 56.0; // Standard height for larger screens
    }
  }

  static EdgeInsets reportItemPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return const EdgeInsets.all(4);
    } else {
      return const EdgeInsets.all(12);
    }
  }

  static EdgeInsets reportItemContentPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return const EdgeInsets.symmetric(horizontal: 8, vertical: 8);
    } else {
      return const EdgeInsets.symmetric(horizontal: 16, vertical: 12);
    }
  }

  static double reportItemIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < mobileBreakpoint) {
      return 14.0;
    } else {
      return 16.0;
    }
  }

  // For PDF preview panel dimensions
  static double pdfPreviewPanelWidth(BuildContext context) {
    return 598.0;
  }

  static double pdfPreviewPanelHeight(BuildContext context) {
    return 725.0;
  }
}
